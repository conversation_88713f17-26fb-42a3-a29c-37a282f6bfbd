import { CustomField } from './custom-field';

/**
 * Interface for default field values in sub-screens
 */
export interface DefaultField {
  defaultField: string;
  defaultValue: string;
}

/**
 * Interface for sub-screen metadata configuration
 * Contains all necessary information to render a sub-screen form
 */
export interface SubScreenMetadata {
  /** Unique identifier for the sub-screen */
  ID: string;
  
  /** Number of columns for layout in this sub-screen */
  columnNumber: number;
  
  /** Array of field definitions for this sub-screen */
  fieldName: CustomField[];
  
  /** Default field values for this sub-screen */
  defaultFields: DefaultField[];
}
