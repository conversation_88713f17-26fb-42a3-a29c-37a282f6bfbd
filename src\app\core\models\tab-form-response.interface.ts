import { CustomField } from './custom-field';
import { SubScreenMetadata, DefaultField } from './sub-screen-metadata.interface';

/**
 * Interface for the complete form response that includes main form and sub-screens
 * This extends the existing dynamic form response structure
 */
export interface TabFormResponse {
  data: {
    // ========================================
    // MAIN FORM PROPERTIES (Existing Structure)
    // ========================================
    /** Main form field definitions */
    fieldName: CustomField[];
    
    /** Number of columns for main form layout */
    columnNumber: number;
    
    /** Default field values for main form */
    defaultFields: DefaultField[];
    
    /** Main form identifier */
    ID: string;
    
    /** Tenant-based flag (existing property) */
    isTenantBased?: boolean;
    
    // ========================================
    // SUB-SCREEN PROPERTIES (New Structure)
    // ========================================
    /** Array of sub-screen identifiers */
    subScreen?: string[];
    
    /** Detailed metadata for each sub-screen */
    subScreensMetadata?: SubScreenMetadata[];
  };
  
  /** Response status */
  status: string;
  
  /** Optional error message */
  message?: any;
}
