/**
 * TABBED FORM EXAMPLE COMPONENT
 * 
 * This component demonstrates how to use the new tabbed-form component
 * with the exact JSON response structure you provided.
 * 
 * It shows:
 * - How to replace existing dynamic-form usage with tabbed-form
 * - How to handle the response structure with main form + sub-screens
 * - How to maintain all existing functionality
 * - How to handle form submission coordination
 */

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Import the new tabbed-form component
import { TabbedFormComponent } from '../components/tabbed-form/tabbed-form.component';
import { TabFormResponse } from '../../core/models/tab-form-response.interface';

@Component({
  selector: 'app-tabbed-form-example',
  imports: [
    CommonModule,
    TabbedFormComponent
  ],
  template: `
    <div class="example-container">
      <h2>Tabbed Form Example</h2>
      <p>This example shows how to use the tabbed-form component with your JSON response structure.</p>
      
      <!-- 
        USAGE: Replace your existing dynamic-form with tabbed-form
        All existing inputs/outputs are preserved
      -->
      <app-tabbed-form
        [tableName]="tableName"
        [screenName]="screenName"
        [data]="sampleResponseData"
        (dataChange)="onFormDataChange($event)"
        (formSubmissionRequested)="onFormSubmissionRequested($event)"
        (validationStateChange)="onValidationStateChange($event)">
      </app-tabbed-form>
      
      <!-- Debug Information -->
      <div class="debug-section" *ngIf="showDebugInfo">
        <h3>Debug Information</h3>
        <div class="debug-item">
          <strong>Current Form Data:</strong>
          <pre>{{ currentFormData | json }}</pre>
        </div>
        <div class="debug-item">
          <strong>Validation State:</strong>
          <pre>{{ validationState | json }}</pre>
        </div>
      </div>
      
      <button (click)="toggleDebugInfo()">
        {{ showDebugInfo ? 'Hide' : 'Show' }} Debug Info
      </button>
    </div>
  `,
  styles: [`
    .example-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .debug-section {
      margin-top: 20px;
      padding: 16px;
      background: #f5f5f5;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .debug-item {
      margin-bottom: 16px;
    }
    
    pre {
      background: #fff;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ccc;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    button {
      margin-top: 16px;
      padding: 8px 16px;
      background: #1976d2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    button:hover {
      background: #1565c0;
    }
  `]
})
export class TabbedFormExampleComponent implements OnInit {
  
  // ========================================
  // COMPONENT PROPERTIES
  // ========================================
  tableName = 'user';
  screenName = 'user_screen';
  showDebugInfo = false;
  currentFormData: any = null;
  validationState: any = {};
  
  // ========================================
  // SAMPLE DATA (Your exact JSON response structure)
  // ========================================
  sampleResponseData: TabFormResponse = {
    "data": {
      "defaultFields": [
        {}
      ],
      "fieldName": [
        {
          "fieldName": "ID",
          "type": "string",
          "noInput": false,
          "mandatory": false,
          "Group": "",
          "isMulti": false,
          "noChange": false
        },
        {
          "fieldName": "user",
          "type": "string",
          "mandatory": true,
          "Group": "",
          "isMulti": false,
          "noChange": false
        },
        {
          "fieldName": "profile",
          "type": "string",
          "mandatory": true,
          "Group": "",
          "isMulti": false,
          "noChange": false
        },
        {
          "fieldName": "language",
          "type": "string",
          "mandatory": false,
          "Group": "",
          "isMulti": false,
          "noChange": false
        }
      ],
      "columnNumber": 3,
      "subScreen": [
        "user,user1",
        "user,user2"
      ],
      "ID": "user,user",
      "subScreensMetadata": [
        {
          "ID": "user,user1",
          "columnNumber": 5,
          "fieldName": [
            {
              "fieldName": "ID",
              "type": "string",
              "noInput": false,
              "mandatory": false,
              "Group": "",
              "isMulti": false,
              "noChange": false
            },
            {
              "fieldName": "start_time",
              "type": "string",
              "mandatory": true,
              "Group": "",
              "isMulti": false,
              "noChange": false
            },
            {
              "fieldName": "end_time",
              "type": "string",
              "mandatory": true,
              "Group": "",
              "isMulti": false,
              "noChange": false
            }
          ],
          "defaultFields": [
            {
              "defaultField": "sdd",
              "defaultValue": "0000"
            }
          ]
        },
        {
          "ID": "user,user2",
          "columnNumber": 3,
          "fieldName": [
            {
              "fieldName": "ID",
              "type": "string",
              "noInput": false,
              "mandatory": false,
              "Group": "",
              "isMulti": false,
              "noChange": false
            },
            {
              "fieldName": "Sign_on_name",
              "type": "string",
              "mandatory": true,
              "Group": "",
              "isMulti": false,
              "noChange": false
            },
            {
              "fieldName": "password.attempts",
              "type": "string",
              "mandatory": true,
              "Group": "",
              "isMulti": false,
              "noChange": false
            },
            {
              "fieldName": "password.validaty",
              "type": "string",
              "mandatory": true,
              "Group": "",
              "isMulti": false,
              "noChange": false
            },
            {
              "fieldName": "role",
              "type": "string",
              "foreginKey": "userRole",
              "Group": "role",
              "mandatory": false,
              "isMulti": false,
              "noChange": false
            },
            {
              "fieldName": "tenant",
              "type": "string",
              "Group": "role",
              "mandatory": false,
              "isMulti": false,
              "noChange": false
            }
          ],
          "defaultFields": [
            {
              "defaultField": "sdd",
              "defaultValue": "10"
            }
          ]
        }
      ]
    },
    "status": "success"
  };
  
  // ========================================
  // LIFECYCLE HOOKS
  // ========================================
  ngOnInit() {
    console.log('Tabbed Form Example initialized with sample data');
  }
  
  // ========================================
  // EVENT HANDLERS
  // ========================================
  
  /**
   * Handles form data changes (same as existing dynamic-form)
   */
  onFormDataChange(data: any) {
    this.currentFormData = data;
    console.log('Form data changed:', data);
  }
  
  /**
   * Handles form submission requests (new functionality)
   */
  onFormSubmissionRequested(data: any) {
    console.log('Form submission requested with data:', data);
    // Here you would handle the submission just like before
    // but now you have data from both main form and all sub-screens
  }
  
  /**
   * Handles validation state changes (new functionality)
   */
  onValidationStateChange(state: any) {
    this.validationState = state;
    console.log('Validation state changed:', state);
  }
  
  /**
   * Toggles debug information display
   */
  toggleDebugInfo() {
    this.showDebugInfo = !this.showDebugInfo;
  }
}
