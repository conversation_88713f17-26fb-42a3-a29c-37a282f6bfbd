/**
 * TAB FORM DATA SERVICE
 * 
 * This service handles data synchronization and coordination between
 * the main form and sub-screen forms in the tabbed form component.
 * 
 * KEY RESPONSIBILITIES:
 * - Parse and process TabFormResponse data
 * - Coordinate data flow between forms
 * - Handle form submission across all tabs
 * - Manage validation state across forms
 * - Preserve existing dynamic-form functionality
 */

import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

import { TabFormResponse } from '../../core/models/tab-form-response.interface';
import { SubScreenTab } from '../../core/models/sub-screen-tab.interface';
import { SubScreenMetadata } from '../../core/models/sub-screen-metadata.interface';

@Injectable({
  providedIn: 'root'
})
export class TabFormDataService {
  
  // ========================================
  // PRIVATE SUBJECTS FOR STATE MANAGEMENT
  // ========================================
  private mainFormDataSubject = new BehaviorSubject<any>(null);
  private subScreenTabsSubject = new BehaviorSubject<SubScreenTab[]>([]);
  private validationStateSubject = new BehaviorSubject<Map<string, boolean>>(new Map());
  private activeTabIndexSubject = new BehaviorSubject<number>(0);
  
  // ========================================
  // PUBLIC OBSERVABLES
  // ========================================
  public mainFormData$ = this.mainFormDataSubject.asObservable();
  public subScreenTabs$ = this.subScreenTabsSubject.asObservable();
  public validationState$ = this.validationStateSubject.asObservable();
  public activeTabIndex$ = this.activeTabIndexSubject.asObservable();
  
  // Combined data observable for parent components
  public combinedFormData$ = combineLatest([
    this.mainFormData$,
    this.subScreenTabs$
  ]).pipe(
    map(([mainData, tabs]) => this.combineFormData(mainData, tabs))
  );
  
  // ========================================
  // DATA PROCESSING METHODS
  // ========================================
  
  /**
   * Processes a TabFormResponse and updates internal state
   * Maintains compatibility with existing dynamic-form structure
   */
  processTabFormResponse(response: TabFormResponse): void {
    if (!response?.data) {
      console.warn('Invalid TabFormResponse received');
      return;
    }
    
    // Process main form data
    const mainFormData = {
      fieldName: response.data.fieldName,
      columnNumber: response.data.columnNumber,
      defaultFields: response.data.defaultFields,
      ID: response.data.ID,
      isTenantBased: response.data.isTenantBased
    };
    
    this.mainFormDataSubject.next(mainFormData);
    
    // Process sub-screens if available
    if (response.data.subScreen && response.data.subScreensMetadata) {
      const tabs = this.createSubScreenTabs(
        response.data.subScreen,
        response.data.subScreensMetadata
      );
      this.subScreenTabsSubject.next(tabs);
    } else {
      this.subScreenTabsSubject.next([]);
    }
    
    // Initialize validation state
    this.initializeValidationState();
  }
  
  /**
   * Creates SubScreenTab objects from response data
   */
  private createSubScreenTabs(subScreenIds: string[], metadata: SubScreenMetadata[]): SubScreenTab[] {
    return subScreenIds.map((id, index) => {
      const meta = metadata.find(m => m.ID === id);
      if (!meta) {
        console.warn(`Sub-screen metadata not found for ID: ${id}`);
        return null;
      }
      
      return {
        id: id,
        label: this.generateTabLabel(id),
        tableName: this.generateTabTableName(id),
        screenName: this.generateTabScreenName(id),
        formData: meta,
        isActive: index === 0,
        isValid: false,
        isInitialized: false
      };
    }).filter(tab => tab !== null) as SubScreenTab[];
  }
  
  /**
   * Generates a user-friendly label for a tab
   */
  private generateTabLabel(id: string): string {
    const parts = id.split(',');
    if (parts.length >= 2) {
      const baseName = parts[0];
      const suffix = parts[1].replace(baseName, '');
      return `${this.capitalizeFirst(baseName)} ${suffix || '1'}`;
    }
    return this.capitalizeFirst(id);
  }
  
  /**
   * Generates table name for sub-screen
   */
  private generateTabTableName(id: string): string {
    return id.replace(',', '_');
  }
  
  /**
   * Generates screen name for sub-screen
   */
  private generateTabScreenName(id: string): string {
    return id.replace(',', '_');
  }
  
  /**
   * Capitalizes first letter of a string
   */
  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  
  // ========================================
  // DATA SYNCHRONIZATION METHODS
  // ========================================
  
  /**
   * Updates main form data
   */
  updateMainFormData(data: any): void {
    this.mainFormDataSubject.next(data);
  }
  
  /**
   * Updates sub-screen form data
   */
  updateSubScreenData(tabId: string, data: any): void {
    const currentTabs = this.subScreenTabsSubject.value;
    const updatedTabs = currentTabs.map(tab => {
      if (tab.id === tabId) {
        return { ...tab, formData: { ...tab.formData, ...data } };
      }
      return tab;
    });
    this.subScreenTabsSubject.next(updatedTabs);
  }
  
  /**
   * Updates validation state for a form
   */
  updateValidationState(formId: string, isValid: boolean): void {
    const currentState = this.validationStateSubject.value;
    const newState = new Map(currentState);
    newState.set(formId, isValid);
    this.validationStateSubject.next(newState);
  }
  
  /**
   * Sets the active tab index
   */
  setActiveTabIndex(index: number): void {
    this.activeTabIndexSubject.next(index);
  }
  
  // ========================================
  // UTILITY METHODS
  // ========================================
  
  /**
   * Combines all form data into a single object
   */
  private combineFormData(mainData: any, tabs: SubScreenTab[]): any {
    const combined: any = {
      mainForm: mainData,
      subScreens: {}
    };
    
    tabs.forEach(tab => {
      combined.subScreens[tab.id] = tab.formData;
    });
    
    return combined;
  }
  
  /**
   * Initializes validation state for all forms
   */
  private initializeValidationState(): void {
    const validationState = new Map<string, boolean>();
    validationState.set('mainForm', false);
    
    const tabs = this.subScreenTabsSubject.value;
    tabs.forEach(tab => {
      validationState.set(tab.id, false);
    });
    
    this.validationStateSubject.next(validationState);
  }
  
  /**
   * Gets current validation summary
   */
  getValidationSummary(): { [key: string]: boolean } {
    const state = this.validationStateSubject.value;
    const summary: { [key: string]: boolean } = {};
    
    state.forEach((isValid, formId) => {
      summary[formId] = isValid;
    });
    
    return summary;
  }
  
  /**
   * Checks if all forms are valid
   */
  areAllFormsValid(): boolean {
    const state = this.validationStateSubject.value;
    return Array.from(state.values()).every(isValid => isValid);
  }
  
  /**
   * Resets all form data and state
   */
  reset(): void {
    this.mainFormDataSubject.next(null);
    this.subScreenTabsSubject.next([]);
    this.validationStateSubject.next(new Map());
    this.activeTabIndexSubject.next(0);
  }
}
