import { SubScreenMetadata } from './sub-screen-metadata.interface';

/**
 * Interface for individual tab configuration
 * Contains all data needed to render a sub-screen as a tab
 */
export interface SubScreenTab {
  /** Unique identifier matching the sub-screen ID */
  id: string;
  
  /** Display label for the tab */
  label: string;
  
  /** Table name for this sub-screen (derived from ID) */
  tableName?: string;
  
  /** Screen name for this sub-screen (derived from ID) */
  screenName?: string;
  
  /** Form data and metadata for this tab */
  formData: SubScreenMetadata;
  
  /** Whether this tab is currently active */
  isActive: boolean;
  
  /** Form validation state */
  isValid?: boolean;
  
  /** Whether this tab has been visited/initialized */
  isInitialized?: boolean;
}
