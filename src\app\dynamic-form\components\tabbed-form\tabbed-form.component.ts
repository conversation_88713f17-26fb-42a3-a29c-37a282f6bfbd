/**
 * TABBED FORM COMPONENT
 * 
 * This component wraps the existing dynamic-form component to provide:
 * - Main form display (always visible and fixed)
 * - Sub-screen tabs (displayed below main form when available)
 * - Complete preservation of existing dynamic-form functionality
 * - Seamless integration with existing styles and behavior
 * 
 * KEY FEATURES:
 * - Zero modification to existing dynamic-form component
 * - Maintains all existing form functionality and validation
 * - Reuses all existing styles through inheritance
 * - Supports both single-form and multi-tab scenarios
 * - Handles data synchronization between main form and sub-screens
 */

import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

// Material Design imports for tabs
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Import existing dynamic-form component (NO MODIFICATIONS)
import { DynamicFormComponent } from '../../dynamic-form.component';

// Import new interfaces and services
import { TabFormResponse } from '../../../core/models/tab-form-response.interface';
import { SubScreenTab } from '../../../core/models/sub-screen-tab.interface';
import { SubScreenMetadata } from '../../../core/models/sub-screen-metadata.interface';
import { TabFormDataService } from '../../services/tab-form-data.service';

@Component({
  selector: 'app-tabbed-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatCardModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    DynamicFormComponent  // Import existing component as-is
  ],
  templateUrl: './tabbed-form.component.html',
  styleUrl: './tabbed-form.component.scss'
})
export class TabbedFormComponent implements OnInit, OnDestroy, AfterViewInit {

  // ========================================
  // INPUT/OUTPUT PROPERTIES (Mirror existing dynamic-form interface)
  // ========================================
  /** Table name for metadata lookup - passed through to dynamic-form */
  @Input() tableName!: string;

  /** Screen name for metadata lookup - passed through to dynamic-form */
  @Input() screenName!: string;

  /** Initial data to populate the form - passed through to dynamic-form */
  @Input() data: any;

  /** Output event when form data changes - mirrors dynamic-form behavior */
  @Output() dataChange = new EventEmitter<any>();

  /** Output event for form submission coordination */
  @Output() formSubmissionRequested = new EventEmitter<any>();

  /** Output event for validation state changes */
  @Output() validationStateChange = new EventEmitter<{ [key: string]: boolean }>();
  
  // ========================================
  // TAB MANAGEMENT PROPERTIES
  // ========================================
  /** Main form data (always displayed) */
  mainFormData: any = null;
  
  /** Array of sub-screen tab configurations */
  subScreenTabs: SubScreenTab[] = [];
  
  /** Currently active tab index */
  activeTabIndex = 0;
  
  /** Whether sub-screens are available */
  hasSubScreens = false;
  
  /** Loading state */
  isLoading = false;
  
  // ========================================
  // VIEW CHILDREN (References to dynamic-form instances)
  // ========================================
  @ViewChild('mainForm') mainForm!: DynamicFormComponent;

  // ========================================
  // FORM STATE MANAGEMENT
  // ========================================
  /** Form data cache for persistence across tab switches */
  private formDataCache: Map<string, any> = new Map();

  /** Validation state for all forms */
  private validationState: Map<string, boolean> = new Map();
  
  // ========================================
  // DEPENDENCY INJECTION
  // ========================================
  constructor(private tabFormDataService: TabFormDataService) {}

  // ========================================
  // LIFECYCLE HOOKS
  // ========================================

  ngOnInit() {
    // Subscribe to service observables for reactive data management
    this.tabFormDataService.mainFormData$.subscribe(data => {
      this.mainFormData = data;
    });

    this.tabFormDataService.subScreenTabs$.subscribe(tabs => {
      this.subScreenTabs = tabs;
      this.hasSubScreens = tabs.length > 0;
    });

    this.tabFormDataService.activeTabIndex$.subscribe(index => {
      this.activeTabIndex = index;
    });

    this.tabFormDataService.validationState$.subscribe(state => {
      this.validationState = state;
      this.validationStateChange.emit(this.tabFormDataService.getValidationSummary());
    });

    // Subscribe to combined form data for parent component
    this.tabFormDataService.combinedFormData$.subscribe(combinedData => {
      this.dataChange.emit(combinedData);
    });
  }

  ngAfterViewInit() {
    // Process initial data if provided
    if (this.data) {
      this.processInitialData(this.data);
    }
  }

  ngOnDestroy() {
    // Reset service state when component is destroyed
    this.tabFormDataService.reset();
  }
  
  // ========================================
  // DATA HANDLING METHODS
  // ========================================
  
  /**
   * Processes initial data (could be TabFormResponse or regular data)
   */
  private processInitialData(data: any) {
    if (this.isTabFormResponse(data)) {
      this.tabFormDataService.processTabFormResponse(data);
    } else {
      // Handle regular data format
      this.tabFormDataService.updateMainFormData(data);
    }
  }

  /**
   * Checks if data is a TabFormResponse
   */
  private isTabFormResponse(data: any): data is TabFormResponse {
    return data && data.data && data.data.fieldName && data.status;
  }

  /**
   * Handles data changes from the main form
   * Preserves all existing dynamic-form behavior and uses service for coordination
   */
  onMainFormDataChange(data: any) {
    this.tabFormDataService.updateMainFormData(data);
    this.formDataCache.set('mainForm', data);
    this.tabFormDataService.updateValidationState('mainForm', this.mainForm?.form?.valid || false);
  }

  /**
   * Handles data changes from sub-screen forms
   * Maintains data synchronization across all forms using service
   */
  onSubFormDataChange(tabIndex: number, data: any) {
    if (this.subScreenTabs[tabIndex]) {
      const tab = this.subScreenTabs[tabIndex];
      this.tabFormDataService.updateSubScreenData(tab.id, data);
      this.formDataCache.set(tab.id, data);
    }
  }
  
  /**
   * Combines data from main form and all sub-screens
   * Maintains the expected data structure for parent components
   */
  private combineAllFormData(): any {
    const combinedData: any = {
      mainForm: this.mainFormData,
      subScreens: {}
    };
    
    this.subScreenTabs.forEach(tab => {
      combinedData.subScreens[tab.id] = tab.formData;
    });
    
    return combinedData;
  }
  
  // ========================================
  // TAB MANAGEMENT METHODS
  // ========================================
  
  /**
   * Handles tab selection changes
   * Ensures proper form initialization and data persistence
   */
  onTabChange(index: number) {
    // Save current tab data before switching
    if (this.activeTabIndex >= 0 && this.activeTabIndex < this.subScreenTabs.length) {
      const currentTab = this.subScreenTabs[this.activeTabIndex];
      currentTab.isActive = false;
    }

    // Switch to new tab
    this.activeTabIndex = index;

    if (this.subScreenTabs[index]) {
      const newTab = this.subScreenTabs[index];
      newTab.isActive = true;

      // Mark tab as initialized if not already
      if (!newTab.isInitialized) {
        newTab.isInitialized = true;

        // Restore cached data if available
        const cachedData = this.formDataCache.get(newTab.id);
        if (cachedData) {
          newTab.formData = { ...newTab.formData, ...cachedData };
        }
      }
    }
  }

  /**
   * Gets the currently active tab
   */
  getActiveTab(): SubScreenTab | null {
    return this.subScreenTabs[this.activeTabIndex] || null;
  }

  /**
   * Switches to a specific tab by ID
   */
  switchToTab(tabId: string): boolean {
    const tabIndex = this.subScreenTabs.findIndex(tab => tab.id === tabId);
    if (tabIndex >= 0) {
      this.onTabChange(tabIndex);
      return true;
    }
    return false;
  }
  
  /**
   * Generates a user-friendly label for a tab based on its ID
   * Converts IDs like "user,user1" to "User 1"
   */
  generateTabLabel(id: string): string {
    const parts = id.split(',');
    if (parts.length >= 2) {
      const baseName = parts[0];
      const suffix = parts[1].replace(baseName, '');
      return `${this.capitalizeFirst(baseName)} ${suffix || '1'}`;
    }
    return this.capitalizeFirst(id);
  }
  
  /**
   * Capitalizes the first letter of a string
   */
  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  
  // ========================================
  // SUB-SCREEN PROCESSING METHODS
  // ========================================

  /**
   * Processes a TabFormResponse to extract main form and sub-screen data
   * This method handles the response structure you provided
   */
  processTabFormResponse(response: TabFormResponse) {
    if (!response?.data) {
      return;
    }

    // Extract main form data (preserve existing structure)
    this.mainFormData = {
      fieldName: response.data.fieldName,
      columnNumber: response.data.columnNumber,
      defaultFields: response.data.defaultFields,
      ID: response.data.ID,
      isTenantBased: response.data.isTenantBased
    };

    // Process sub-screens if available
    if (response.data.subScreen && response.data.subScreensMetadata) {
      this.hasSubScreens = true;
      this.subScreenTabs = this.createSubScreenTabs(
        response.data.subScreen,
        response.data.subScreensMetadata
      );
    } else {
      this.hasSubScreens = false;
      this.subScreenTabs = [];
    }
  }

  /**
   * Creates tab configurations from sub-screen data
   * Converts the response structure into usable tab objects
   */
  private createSubScreenTabs(subScreenIds: string[], metadata: SubScreenMetadata[]): SubScreenTab[] {
    return subScreenIds.map((id, index) => {
      const meta = metadata.find(m => m.ID === id);
      if (!meta) {
        console.warn(`Sub-screen metadata not found for ID: ${id}`);
        return null;
      }

      return {
        id: id,
        label: this.generateTabLabel(id),
        tableName: this.generateTabTableName(id),
        screenName: this.generateTabScreenName(id),
        formData: meta,
        isActive: index === 0, // First tab is active by default
        isValid: false,
        isInitialized: false
      };
    }).filter(tab => tab !== null) as SubScreenTab[];
  }

  /**
   * Generates table name for sub-screen based on ID
   * Maintains consistency with existing naming conventions
   */
  private generateTabTableName(id: string): string {
    // Use the base table name with sub-screen suffix
    return this.tableName ? `${this.tableName}_${id.replace(',', '_')}` : id;
  }

  /**
   * Generates screen name for sub-screen based on ID
   * Maintains consistency with existing naming conventions
   */
  private generateTabScreenName(id: string): string {
    // Use the base screen name with sub-screen suffix
    return this.screenName ? `${this.screenName}_${id.replace(',', '_')}` : id;
  }

  // ========================================
  // FORM VALIDATION AND SUBMISSION
  // ========================================

  /**
   * Validates all forms (main + sub-screens)
   * Maintains existing validation behavior
   */
  validateAllForms(): boolean {
    let allValid = true;

    // Validate main form
    if (this.mainForm && this.mainForm.form) {
      const mainFormValid = this.mainForm.form.valid;
      allValid = allValid && mainFormValid;
      this.updateValidationState('mainForm', mainFormValid);
    }

    // Validate all initialized sub-screen forms
    this.subScreenTabs.forEach(tab => {
      if (tab.isInitialized) {
        const tabValid = this.validationState.get(tab.id) || false;
        allValid = allValid && tabValid;
      }
    });

    return allValid;
  }

  /**
   * Gets validation summary for all forms
   */
  getValidationSummary(): { [key: string]: boolean } {
    const summary: { [key: string]: boolean } = {};

    // Main form validation
    summary['mainForm'] = this.validationState.get('mainForm') || false;

    // Sub-screen validation
    this.subScreenTabs.forEach(tab => {
      summary[tab.id] = this.validationState.get(tab.id) || false;
    });

    return summary;
  }

  /**
   * Forces validation of all forms
   */
  forceValidateAllForms(): void {
    // Trigger validation on main form
    if (this.mainForm && this.mainForm.form) {
      this.mainForm.form.markAllAsTouched();
      this.updateValidationState('mainForm', this.mainForm.form.valid);
    }

    // Note: Sub-form validation will be handled by their respective dynamic-form instances
  }

  /**
   * Checks if the component should display tabs
   * Returns true if sub-screens are available
   */
  shouldDisplayTabs(): boolean {
    return this.hasSubScreens && this.subScreenTabs.length > 0;
  }

  // ========================================
  // FORM SUBMISSION COORDINATION
  // ========================================

  /**
   * Coordinates form submission across all forms
   * Maintains existing submission behavior while adding multi-form support
   */
  async submitAllForms(): Promise<boolean> {
    try {
      // Validate all forms first
      const allValid = this.validateAllForms();
      if (!allValid) {
        console.warn('Cannot submit: Some forms are invalid');
        return false;
      }

      // Collect all form data
      const combinedData = this.tabFormDataService.combinedFormData$;

      // Emit submission request for parent component to handle
      this.formSubmissionRequested.emit(combinedData);

      return true;
    } catch (error) {
      console.error('Error during form submission:', error);
      return false;
    }
  }

  /**
   * Public method to trigger form submission from parent components
   */
  triggerSubmission(): Promise<boolean> {
    return this.submitAllForms();
  }

  /**
   * Gets the current state of all forms for external access
   */
  getCurrentFormState(): any {
    return {
      mainForm: this.mainFormData,
      subScreens: this.subScreenTabs.map(tab => ({
        id: tab.id,
        label: tab.label,
        data: tab.formData,
        isValid: tab.isValid,
        isInitialized: tab.isInitialized
      })),
      validationSummary: this.tabFormDataService.getValidationSummary(),
      allFormsValid: this.tabFormDataService.areAllFormsValid()
    };
  }

  /**
   * Allows external components to process TabFormResponse
   */
  processTabFormResponse(response: TabFormResponse): void {
    this.tabFormDataService.processTabFormResponse(response);
  }
}
