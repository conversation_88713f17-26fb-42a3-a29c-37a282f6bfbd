import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { TabbedFormComponent } from './tabbed-form.component';
import { DynamicFormComponent } from '../../dynamic-form.component';

describe('TabbedFormComponent', () => {
  let component: TabbedFormComponent;
  let fixture: ComponentFixture<TabbedFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        MatTabsModule,
        MatCardModule,
        MatDividerModule,
        BrowserAnimationsModule,
        TabbedFormComponent,
        DynamicFormComponent
      ]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(TabbedFormComponent);
    component = fixture.componentInstance;
    
    // Set required inputs
    component.tableName = 'test-table';
    component.screenName = 'test-screen';
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display main form only when no sub-screens', () => {
    component.hasSubScreens = false;
    component.subScreenTabs = [];
    fixture.detectChanges();
    
    expect(component.shouldDisplayTabs()).toBeFalsy();
  });

  it('should display tabs when sub-screens are available', () => {
    component.hasSubScreens = true;
    component.subScreenTabs = [
      {
        id: 'test,tab1',
        label: 'Test Tab 1',
        formData: { ID: 'test,tab1', columnNumber: 2, fieldName: [], defaultFields: [] },
        isActive: true,
        isInitialized: false
      }
    ];
    fixture.detectChanges();
    
    expect(component.shouldDisplayTabs()).toBeTruthy();
  });

  it('should generate proper tab labels', () => {
    const label1 = component.generateTabLabel('user,user1');
    const label2 = component.generateTabLabel('profile,profile2');
    
    expect(label1).toBe('User 1');
    expect(label2).toBe('Profile 2');
  });

  it('should handle main form data changes', () => {
    spyOn(component.dataChange, 'emit');
    const testData = { field1: 'value1' };

    component.onMainFormDataChange(testData);

    expect(component.dataChange.emit).toHaveBeenCalled();
  });

  it('should handle sub-form data changes', () => {
    component.subScreenTabs = [
      {
        id: 'test,tab1',
        label: 'Test Tab 1',
        formData: { ID: 'test,tab1', columnNumber: 2, fieldName: [], defaultFields: [] },
        isActive: true,
        isInitialized: false
      }
    ];

    spyOn(component.dataChange, 'emit');
    const testData = { field2: 'value2' };

    component.onSubFormDataChange(0, testData);

    expect(component.dataChange.emit).toHaveBeenCalled();
  });

  it('should process TabFormResponse correctly', () => {
    const sampleResponse = {
      data: {
        fieldName: [
          { fieldName: 'user', type: 'string', mandatory: true, Group: '', isMulti: false, noChange: false },
          { fieldName: 'profile', type: 'string', mandatory: true, Group: '', isMulti: false, noChange: false }
        ],
        columnNumber: 2,
        defaultFields: [],
        ID: 'test,main',
        subScreen: ['test,sub1'],
        subScreensMetadata: [
          {
            ID: 'test,sub1',
            columnNumber: 3,
            fieldName: [
              { fieldName: 'subField', type: 'string', mandatory: false, Group: '', isMulti: false, noChange: false }
            ],
            defaultFields: []
          }
        ]
      },
      status: 'success'
    };

    component.processTabFormResponse(sampleResponse);

    expect(component.hasSubScreens).toBeTruthy();
    expect(component.subScreenTabs.length).toBe(1);
    expect(component.subScreenTabs[0].id).toBe('test,sub1');
  });
});
