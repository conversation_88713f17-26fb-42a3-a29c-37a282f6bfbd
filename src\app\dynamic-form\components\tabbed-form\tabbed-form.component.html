<!-- 
  TABBED FORM TEMPLATE
  
  This template provides a wrapper around the existing dynamic-form component
  to support main form + sub-screen tabs without modifying existing functionality.
  
  STRUCTURE:
  1. Main Form Section (Always Visible)
  2. Tab Navigation (Only when sub-screens exist)
  3. Sub-Screen Forms (Rendered in tabs)
  
  PRESERVATION:
  - All existing dynamic-form functionality is preserved
  - All existing styles are inherited
  - All existing behavior remains intact
-->

<div class="tabbed-form-container">
  
  <!-- ======================================== -->
  <!-- MAIN FORM SECTION (Always Visible) -->
  <!-- ======================================== -->
  <!-- 
    This section displays the main form using the existing dynamic-form component
    The component is used exactly as it was before, with no modifications
  -->
  <div class="main-form-section">
    <div class="main-form-header">
      <h3 class="main-form-title">Main Form</h3>
    </div>
    
    <!-- 
      EXISTING DYNAMIC-FORM COMPONENT (UNMODIFIED)
      All inputs and outputs are preserved exactly as they were
    -->
    <app-dynamic-form 
      #mainForm
      [tableName]="tableName"
      [screenName]="screenName"
      [data]="mainFormData"
      (dataChange)="onMainFormDataChange($event)">
    </app-dynamic-form>
  </div>

  <!-- ======================================== -->
  <!-- TAB NAVIGATION SECTION (Conditional) -->
  <!-- ======================================== -->
  <!-- 
    This section only appears when sub-screens are available
    Uses Material Design tabs for consistent UI experience
  -->
  @if (shouldDisplayTabs()) {
    <div class="tab-navigation-section">
      
      <!-- Divider between main form and tabs -->
      <mat-divider class="form-divider"></mat-divider>
      
      <!-- Tab Navigation Header -->
      <div class="tab-header">
        <h4 class="tab-section-title">Sub-Screens</h4>
      </div>
      
      <!-- Material Design Tab Group -->
      <mat-tab-group 
        [(selectedIndex)]="activeTabIndex"
        (selectedIndexChange)="onTabChange($event)"
        class="sub-screen-tabs">
        
        <!-- ======================================== -->
        <!-- INDIVIDUAL TAB CONTENT -->
        <!-- ======================================== -->
        <!-- 
          Each tab contains its own dynamic-form instance
          This preserves all existing functionality per tab
        -->
        @for (tab of subScreenTabs; track tab.id) {
          <mat-tab [label]="tab.label">
            
            <!-- Tab Content Container -->
            <div class="tab-content">
              
              <!-- 
                EXISTING DYNAMIC-FORM COMPONENT (UNMODIFIED)
                Each sub-screen gets its own instance of the dynamic-form
                This ensures complete isolation and preservation of functionality
              -->
              <app-dynamic-form 
                [tableName]="tab.tableName"
                [screenName]="tab.screenName"
                [data]="tab.formData"
                (dataChange)="onSubFormDataChange($index, $event)">
              </app-dynamic-form>
              
            </div>
          </mat-tab>
        }
        
      </mat-tab-group>
    </div>
  }
  
</div>

<!-- ======================================== -->
<!-- LOADING STATE (Optional) -->
<!-- ======================================== -->
<!-- 
  Loading indicator that can be shown during data processing
  Maintains consistency with existing loading patterns
-->
@if (isLoading) {
  <div class="loading-overlay">
    <div class="loading-content">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading form data...</p>
    </div>
  </div>
}
