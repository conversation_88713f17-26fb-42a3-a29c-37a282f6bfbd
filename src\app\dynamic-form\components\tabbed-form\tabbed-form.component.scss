/**
 * TABBED FORM STYLES
 * 
 * This stylesheet extends and inherits from the existing dynamic-form styles
 * to ensure complete visual consistency while adding tab functionality.
 * 
 * APPROACH:
 * - Import and extend existing dynamic-form styles
 * - Add minimal new styles only for tab-specific elements
 * - Preserve all existing visual behavior and responsive design
 * - Maintain Material Design consistency
 */

/* ======================================== */
/* INHERIT EXISTING DYNAMIC-FORM STYLES */
/* ======================================== */
/*
  We inherit styles through CSS class extension rather than @import
  to avoid potential circular dependencies and ensure proper inheritance
*/

/* ======================================== */
/* TABBED FORM CONTAINER */
/* ======================================== */
/* 
  Main container that wraps both main form and tab sections
  Maintains existing spacing and layout patterns
*/
.tabbed-form-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  
  /* Ensure consistent background and spacing */
  background: inherit;
  box-sizing: border-box;
}

/* ======================================== */
/* MAIN FORM SECTION */
/* ======================================== */
/* 
  Styles for the main form section (always visible)
  Extends existing form container styles
*/
.main-form-section {
  /* Base styling that matches existing dynamic-form patterns */
  width: 100%;
  position: relative;
  margin-bottom: 24px;
  padding: 16px;
  padding-bottom: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  /* Subtle border to separate from tabs */
  border-bottom: 1px solid #e0e0e0;
  
  .main-form-header {
    margin-bottom: 16px;
    
    .main-form-title {
      font-size: 1.25rem;
      font-weight: 500;
      color: #333;
      margin: 0;
      padding: 0;
    }
  }
  
  /* Ensure the nested dynamic-form inherits all styles */
  app-dynamic-form {
    display: block;
    width: 100%;
  }
}

/* ======================================== */
/* TAB NAVIGATION SECTION */
/* ======================================== */
/* 
  Styles for the tab navigation area
  Integrates seamlessly with Material Design tabs
*/
.tab-navigation-section {
  width: 100%;
  margin-top: 16px;
  
  .form-divider {
    margin: 16px 0;
    border-color: #e0e0e0;
  }
  
  .tab-header {
    margin-bottom: 16px;
    
    .tab-section-title {
      font-size: 1.1rem;
      font-weight: 500;
      color: #555;
      margin: 0;
      padding: 0;
    }
  }
}

/* ======================================== */
/* MATERIAL DESIGN TAB CUSTOMIZATION */
/* ======================================== */
/*
  Customizes Material Design tabs to match existing form styling
  Maintains visual consistency with the rest of the application
*/
.sub-screen-tabs {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  /* Tab header styling */
  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;

    .mat-mdc-tab-label {
      /* Match existing button/form styling */
      font-weight: 500;
      text-transform: none;
      color: #666;
      transition: all 0.3s ease;

      &.mdc-tab--active {
        color: #1976d2;
        background: rgba(25, 118, 210, 0.1);
      }

      &:hover:not(.mdc-tab--active) {
        color: #333;
        background: rgba(0, 0, 0, 0.05);
      }

      /* Validation indicator */
      &.tab-invalid {
        color: #d32f2f;

        &::after {
          content: '⚠';
          margin-left: 4px;
          font-size: 0.8em;
        }
      }

      &.tab-valid {
        &::after {
          content: '✓';
          margin-left: 4px;
          color: #4caf50;
          font-size: 0.8em;
        }
      }
    }
  }

  /* Tab content area */
  .mat-mdc-tab-body-wrapper {
    .mat-mdc-tab-body {
      .mat-mdc-tab-body-content {
        padding: 16px;
        overflow: visible;
        background: #fff;
      }
    }
  }
}

/* ======================================== */
/* TAB CONTENT STYLING */
/* ======================================== */
/* 
  Styles for individual tab content areas
  Ensures each tab's dynamic-form inherits all existing styles
*/
.tab-content {
  width: 100%;
  
  /* Ensure nested dynamic-form components inherit all styles */
  app-dynamic-form {
    display: block;
    width: 100%;

    /* Reset any conflicting styles to let dynamic-form handle its own styling */
    margin: 0;
    padding: 0;
    background: transparent;
    box-shadow: none;
    border: none;
  }
}

/* ======================================== */
/* LOADING STATE STYLES */
/* ======================================== */
/* 
  Loading overlay that matches existing loading patterns
  Maintains visual consistency with existing loading states
*/
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    
    p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }
  }
}

/* ======================================== */
/* RESPONSIVE DESIGN */
/* ======================================== */
/* 
  Responsive adjustments that maintain existing breakpoints
  Ensures tabs work well on all screen sizes
*/

/* Mobile screens */
@media (max-width: 768px) {
  .tabbed-form-container {
    padding: 8px;
  }
  
  .main-form-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    
    .main-form-header .main-form-title {
      font-size: 1.1rem;
    }
  }
  
  .tab-navigation-section {
    .tab-header .tab-section-title {
      font-size: 1rem;
    }
  }
  
  .sub-screen-tabs {
    .mat-mdc-tab-header {
      .mat-mdc-tab-label {
        font-size: 0.9rem;
        min-width: 80px;
      }
    }
  }
}

/* Tablet screens */
@media (min-width: 769px) and (max-width: 1024px) {
  .tabbed-form-container {
    padding: 12px;
  }
}

/* Desktop screens */
@media (min-width: 1025px) {
  .tabbed-form-container {
    padding: 16px;
  }
}
